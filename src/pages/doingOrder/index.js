import { Component } from "react";
import { Table, Button } from "antd";
import { MenuFoldOutlined } from "@ant-design/icons";
import { AuditedTable } from "@/components/OrderTable/styles";
import OrderDetailDrawer from "@/components/OrderDetailDrawer";
import { requestMyDoingOrder } from "@/request/api";
import RoleTagCell from "@/components/RoleTag/RoleTagCell";
import OrderPagination from "../../components/OrderPagination";
import styled from "styled-components";


// css-js start ↓↓↓
const DoingTable = styled(Table)`
  margin-top: 16px;
  background: #fff;
  border-radius: 12px;
  padding: 0 0; /* 由外层容器统一内边距 */
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);

  .ant-table-thead > tr > th {
    background-color: #fff;
    font-weight: 500;
    border-bottom: 1px solid #f0f2f5;
  }
  .ant-table-tbody > tr > td {
    border-bottom: 1px solid #f5f7fb;
  }
  .ant-table-tbody > tr:hover > td {
    background-color: #f5f7fb;
  }
`
// css-js end   ↑↑↑

export default class DoingOrder extends Component {
  state = {
    dataSource: [],
    pageSize: 10,
    pageNum: 1,
    total: 0,
    filter_by_role: [], // 为迭代三预留角色筛选参数
    // 详情抽屉状态
    selectedOrderId: null,
    selectedOrderType: null,
  };

  // 获取表格列配置
  getColumns = () => {
    return [
      {
        title: "单号",
        dataIndex: "orderID",
        key: "orderID",
        fixed: "left",
        align: "center",
        render: (text, record) => (
          <RoleTagCell roleType={record.role_type}>
            <span>{text}</span>
          </RoleTagCell>
        ),
        // 更新筛选框标签为申请和抄送
        filters: [
          { text: '[申请]', value: 'APPLICANT' },
          { text: '[抄送]', value: 'CC_TO_ME' },
        ],
        filteredValue: this.state.filter_by_role.length > 0 ? this.state.filter_by_role : null,
        filterMultiple: true, // 支持多选
      },
      {
        title: "工单类型",
        dataIndex: "orderTypeName",
        key: "orderTypeName",
        align: "center",
      },
      {
        title: "总节点数",
        dataIndex: "totalStageNum",
        key: "totalStageNum",
        align: "center",
      },
      {
        title: "当前节点 / 节点名称",
        dataIndex: "currentStage",
        key: "currentStage",
        align: "center",
      },
      {
        title: "节点负责人",
        dataIndex: "stageOperator",
        key: "stageOperator",
        align: "center",
      },
      {
        title: "工单状态",
        dataIndex: "resultDesc",
        key: "resultDesc",
        align: "center",
      },
      {
        title: "申请人",
        dataIndex: "propose",
        key: "propose",
        align: "center",
      },
      {
        title: "运维负责人",
        dataIndex: "opsOwner",
        key: "opsOwner",
        align: "center",
      },
      {
        title: "申请日期",
        dataIndex: "ctime",
        key: "ctime",
        align: "center",
        sorter: (a, b) => a.age - b.age,
      },
      {
        title: "详情",
        key: "detail",
        dataIndex: "detail",
        fixed: "right",
        align: "center",
        render: (_, record) => (
          <Button
            type="primary"
            size="small"
            onClick={() => this.handleViewDetail(record)}
            icon={<MenuFoldOutlined />}
            style={{
              backgroundColor: '#4f46e5',
              borderColor: '#4f46e5',
              color: 'white'
            }}
          >
            详情
          </Button>
        ),
      },
    ];
  };

  // 根据is_cc字段判断角色类型
  getRoleType = (is_cc) => {
    return is_cc === 1 ? 'CC_TO_ME' : 'APPLICANT';
  };

  componentDidMount() {
    this.requestMyDoingPageOrder();
  }

  // 处理查看详情
  handleViewDetail = (record) => {
    this.setState({
      selectedOrderId: record.orderID,
      selectedOrderType: record.orderType
    });
  };

  // 关闭详情抽屉
  handleDetailDrawerClose = () => {
    this.setState({
      selectedOrderId: null,
      selectedOrderType: null
    });
  };

  // 请求进行中工单数据
  requestMyDoingPageOrder = () => {
    const { pageNum, pageSize, filter_by_role } = this.state;
    requestMyDoingOrder({
      page: pageNum,
      page_size: pageSize,
      filter_by_role: filter_by_role // 传递角色筛选参数
    }).then((data) => {
      var orders = data.orders.map((item, index) => {
        return {
          key: index,
          orderID: item.order_id,
          orderType: item.order_type,
          orderTypeName: item.order_type_name,
          totalStageNum: item.total_stage_num,
          currentStage: item.current_stage + " / " + (item.current_stage_name),
          stageOperator: item.stage_operator,
          resultDesc: item.result_desc,
          propose: item.proposer_email,
          opsOwner: item.ops_owner_email,
          ctime: item.apply_datetime,
          trace: "action",
          role_type: this.getRoleType(item.is_cc), // 根据is_cc字段判断角色类型
        };
      });
      this.setState({
        dataSource: orders,
        total: data.total || 0,
      });
    });
  };

  // 处理分页变化
  handlePageChange = (pageNum) => {
    this.setState({ pageNum }, this.requestMyDoingPageOrder);
  };

  // 处理每页显示数量变化
  handlePageSizeChange = (current, size) => {
    this.setState({ pageNum: 1, pageSize: size }, this.requestMyDoingPageOrder);
  };

  // 处理表格变化（分页、排序、筛选）
  handleTableChange = (pagination, filters, sorter) => {
    // 处理角色筛选逻辑
    const roleFilters = filters.orderID || [];
    this.setState({
      filter_by_role: roleFilters,
      pageNum: 1 // 筛选时重置到第一页
    }, () => {
      // 重新请求数据
      this.requestMyDoingPageOrder();
    });
  };

  render() {
    const { dataSource, total, pageNum, pageSize, selectedOrderId, selectedOrderType } = this.state;

    return (
      <div>
        <DoingTable
          dataSource={dataSource}
          columns={this.getColumns()}
          size={"middle"}
          pagination={false}
          onChange={this.handleTableChange} // 添加表格变化处理
        />

        {/* 分页组件 */}
        <OrderPagination
          total={total}
          current={pageNum}
          pageSize={pageSize}
          pageSizeOptions={['10', '20', '30', '50', '100']}
          onPageChange={this.handlePageChange}
          onPageSizeChange={this.handlePageSizeChange}
        />

        {/* 工单详情抽屉 */}
        {selectedOrderId && (
          <OrderDetailDrawer
            key={`${selectedOrderId}-${selectedOrderType}`}
            orderID={selectedOrderId}
            orderType={selectedOrderType}
            visible={true}
            onClose={this.handleDetailDrawerClose}
          />
        )}
      </div>
    );
  }
}
