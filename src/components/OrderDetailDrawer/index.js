"use client"
import React, { useState, useEffect } from "react";
import { message, Drawer, But<PERSON> } from "antd";
import { MenuFoldOutlined } from "@ant-design/icons";
import { requestFlowApproval, requestFlowAuditTurn, requestOrderDetail } from "@/request/api";
import styled from "styled-components";
import Cookies from "js-cookie";
import {
  X,
  User,
  FileText,
  MessageSquare,
  CheckCircle,
  Clock,
  AlertCircle,
  UserCheck,
  ThumbsUp,
  ThumbsDown,
} from "lucide-react";

// 自定义UI组件样式 - 模仿shadcn/ui
const CustomButton = styled.button`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
  border: 1px solid transparent;
  padding: 8px 16px;
  height: 36px;
  
  &.variant-default {
    background-color: #0f172a;
    color: #f8fafc;
    &:hover {
      background-color: #1e293b;
    }
  }
  
  &.variant-outline {
    border: 1px solid #e2e8f0;
    background-color: transparent;
    &:hover {
      background-color: #f1f5f9;
    }
  }
  
  &.variant-ghost {
    background-color: transparent;
    &:hover {
      background-color: #f1f5f9;
    }
  }
  
  &.variant-destructive {
    background-color: #dc2626;
    color: #f8fafc;
    &:hover {
      background-color: #b91c1c;
    }
  }
  
  &.size-sm {
    height: 32px;
    padding: 6px 12px;
    font-size: 13px;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const Card = styled.div`
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  background-color: #ffffff;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
`;

const CardHeader = styled.div`
  display: flex;
  flex-direction: column;
  padding: 24px;
  padding-bottom: 0;
`;

const CardTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  line-height: 1;
  margin: 0;
`;

const CardContent = styled.div`
  padding: 24px;
  padding-top: 0;
`;

const Badge = styled.span`
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s;
  border: 1px solid transparent;
  
  &.bg-green-100 {
    background-color: #dcfce7;
    color: #166534;
    border-color: #bbf7d0;
  }
  
  &.bg-blue-100 {
    background-color: #dbeafe;
    color: #1e40af;
    border-color: #bfdbfe;
  }
  
  &.bg-orange-100 {
    background-color: #fed7aa;
    color: #c2410c;
    border-color: #fdba74;
  }
  
  &.bg-red-100 {
    background-color: #fee2e2;
    color: #dc2626;
    border-color: #fecaca;
  }
  
  &.bg-yellow-100 {
    background-color: #fef3c7;
    color: #d97706;
    border-color: #fde68a;
  }
  
  &.bg-gray-100 {
    background-color: #f3f4f6;
    color: #374151;
    border-color: #e5e7eb;
  }
`;

const CustomTextarea = styled.textarea`
  display: flex;
  min-height: 80px;
  width: 100%;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  background-color: #ffffff;
  padding: 12px;
  font-size: 14px;
  resize: vertical;
  
  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }
  
  &::placeholder {
    color: #9ca3af;
  }
`;

const CustomInput = styled.input`
  display: flex;
  height: 36px;
  width: 100%;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  background-color: #ffffff;
  padding: 8px 12px;
  font-size: 14px;
  
  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }
  
  &::placeholder {
    color: #9ca3af;
  }
`;

const CustomLabel = styled.label`
  font-size: 14px;
  font-weight: 500;
  line-height: 1;
  color: #374151;
`;

// Dialog组件样式 - 提高z-index确保在抽屉之上
const DialogOverlay = styled.div`
  position: fixed;
  inset: 0;
  z-index: 2000;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
`;

const DialogContent = styled.div`
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  width: 100%;
  max-width: 32rem;
  max-height: 85vh;
  overflow-y: auto;
`;

const DialogHeader = styled.div`
  display: flex;
  flex-direction: column;
  padding: 24px;
  padding-bottom: 16px;
`;

const DialogTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
  line-height: 1;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const DialogFooter = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 24px;
  padding-top: 16px;
  
  @media (min-width: 640px) {
    flex-direction: row;
    justify-content: flex-end;
  }
`;

export default function OrderDetailDrawer({ orderID, orderType, visible, onClose, title }) {
  // 状态管理 - 保持原有的所有状态变量
  const [auditButtonVisible, setAuditButtonVisible] = useState(false);
  const [turnAuditButtonVisible, setTurnAuditButtonVisible] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);

  const [remark, setRemark] = useState("");
  const [stageInfos, setStageInfos] = useState([]);
  const [orderInfo, setOrderInfo] = useState({ info: {} });

  const [comment, setComment] = useState("");
  const [showOwnerChangeModal, setShowOwnerChangeModal] = useState(false);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [newOwnerEmail, setNewOwnerEmail] = useState("");
  const [approvalAction, setApprovalAction] = useState("approve");


  
  const cnMap = {
    "common": {
      "title": [3, "标题"],
      "ops_audit_email": [3, "运维审批人"],
      "apply_msg": [6, "申请理由"],
    },
    "sql_audit_execute": {
      "db_host": [3, "数据库IP"],
      "check_md5": [3, "校验ID"],
      "sql": [6, "sql语句"]
    },
    "sql_audit_file_execute": {
      "db_host": [3, "数据库IP"],
      "check_md5": [3, "校验ID"],
      "sql": [6, "sql语句"]
    },
    "server_jump_impower": {
      "server_ip": [6, "申请登陆权限IP"],
      "apply_msg": [6, "申请理由"],
    },
    "pointed_approver": {
      "approver_email": [6, "审批人"],
      "apply_msg": [6, "申请理由"]
    },
    "cdn_create_execute": {
      "approver_email": [6, "审批人"],
      "apply_msg": [6, "申请理由"]
    },
    "domain_resolve_execute": {
      "approver_email": [6, "审批人"],
      "apply_msg": [6, "申请理由"]
    },
    "long_term_token_new_refresh": {
      "approver_email": [6, "审批人"],
      "apply_msg": [6, "申请理由"]
    },
    "long_term_token_view": {
      "approver_email": [6, "审批人"],
      "apply_msg": [6, "申请理由"]
    }
  };

  // 工具函数 - 获取中文映射
  const getCnMap = (orderType) => {
    if (orderType in cnMap) {
      return cnMap[orderType];
    }
    return {
      "apply_msg": [6, "申请理由"]
    };
  };

  // 数据获取函数
  const fetchOrderDetail = async (orderID) => {
    try {
      const userEmail = Cookies.get("user_email");
      const data = await requestOrderDetail({ order_id: orderID });

      if (data === null) {
        return;
      }

      const stageInfosData = data.stage_infos;
      const orderInfoData = data.order_info;

      // 初始化按钮显示状态
      let auditButtonVisibleState = false;
      let turnAuditButtonVisibleState = false;

      if (stageInfosData[orderInfoData.current_stage - 1].stage_operator === userEmail && orderInfoData.result === 0) {
        auditButtonVisibleState = true;
      }
      if (stageInfosData[orderInfoData.current_stage - 1].stage_operator === userEmail && orderInfoData.result === 0 && orderInfoData.current_stage < orderInfoData.total_stage_num) {
        turnAuditButtonVisibleState = true;
      }

      // 解析详情字段
      orderInfoData.info = JSON.parse(orderInfoData.info);
      orderInfoData.info["apply_msg"] = orderInfoData.apply_msg;

      setAuditButtonVisible(auditButtonVisibleState);
      setTurnAuditButtonVisible(turnAuditButtonVisibleState);
      setStageInfos(stageInfosData);
      setOrderInfo(orderInfoData);
      setDrawerVisible(true);
    } catch (err) {
      console.log(err);
    }
  };

  // useEffect替代componentDidMount和componentDidUpdate
  useEffect(() => {
    const isVisible = visible === true;
    if (isVisible && orderID) {
      fetchOrderDetail(orderID);
    }
  }, [visible, orderID]);

  // 状态图标和颜色函数
  const getStatusIcon = (status) => {
    switch (status) {
      case "已完成":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "处理中":
        return <Clock className="h-4 w-4 text-blue-600" />;
      case "待审批":
        return <AlertCircle className="h-4 w-4 text-orange-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "已完成":
        return "bg-green-100";
      case "处理中":
        return "bg-blue-100";
      case "待审批":
        return "bg-orange-100";
      default:
        return "bg-gray-100";
    }
  };

  // 根据工单结果获取状态显示
  const getOrderStatus = (result) => {
    switch (result) {
      case 0:
        return { text: "处理中", color: "bg-blue-100" };
      case 1:
        return { text: "已完成", color: "bg-green-100" };
      case 2:
        return { text: "已驳回", color: "bg-red-100" };
      default:
        return { text: "未知", color: "bg-gray-100" };
    }
  };



  // 事件处理函数 - 保持原有的API调用逻辑
  const handleOwnerChange = async () => {
    try {
      await requestFlowAuditTurn({
        order_id: orderID,
        audit_email: newOwnerEmail.trim()
      });
      message.success("审批人变更成功");
      setShowOwnerChangeModal(false);
      setNewOwnerEmail("");
      // 重新获取数据
      fetchOrderDetail(orderID);
    } catch (err) {
      message.error("审批人变更失败");
      console.log(err);
    }
  };

  const handleApproval = async () => {
    try {
      const result = approvalAction === "approve" ? 1 : 2;
      await requestFlowApproval({
        order_id: orderID,
        result: result,
        remark: remark.trim()
      });
      message.success(`审批${approvalAction === "approve" ? "通过" : "驳回"}成功`);
      setShowApprovalModal(false);
      setApprovalAction("approve");
      setRemark("");
      // 重新获取数据
      fetchOrderDetail(orderID);
    } catch (err) {
      message.error(`审批${approvalAction === "approve" ? "通过" : "驳回"}失败`);
      console.log(err);
    }
  };

  const handleClose = () => {
    if (onClose) {
      onClose();
    } else {
      setDrawerVisible(false);
    }
  };

  // 如果不可见，不渲染
  const isOpen = visible !== undefined ? visible : drawerVisible;

  return (
    <>
      {/* 只有在没有外部控制时才显示按钮 */}
      {visible === undefined && (
        <Button
          type="primary"
          size="small"
          onClick={() => {
            if (orderID) {
              fetchOrderDetail(orderID);
            }
          }}
          icon={<MenuFoldOutlined />}
        >
          {title || "详情"}
        </Button>
      )}

      {/* 抽屉内容 - 使用Antd的Drawer组件 */}
      <Drawer
        width={800}
        placement="right"
        closable={true}
        onClose={handleClose}
        open={isOpen && !!orderInfo.order_id}
        title={
          <div style={{
            fontSize: '18px',
            fontWeight: '600',
            color: '#1f2937',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            <FileText style={{ width: '20px', height: '20px' }} />
            审批流程跟踪
          </div>
        }
        styles={{
          header: {
            borderBottom: '1px solid #e5e7eb',
            paddingBottom: '16px'
          }
        }}
      >
          <div style={{ padding: '24px', backgroundColor: '#f9fafb' }}>
            {/* 操作按钮 */}
            <div style={{ display: 'flex', gap: '12px', marginBottom: '24px' }}>
              {turnAuditButtonVisible && (
                <CustomButton
                  className="variant-outline"
                  onClick={() => setShowOwnerChangeModal(true)}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    padding: '8px 16px',
                    borderRadius: '6px'
                  }}
                >
                  <UserCheck style={{ width: '16px', height: '16px' }} />
                  负责人变更
                </CustomButton>
              )}
              {auditButtonVisible && (
                <CustomButton
                  className="variant-default"
                  onClick={() => setShowApprovalModal(true)}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    padding: '8px 16px',
                    borderRadius: '6px'
                  }}
                >
                  <ThumbsUp style={{ width: '16px', height: '16px' }} />
                  审批
                </CustomButton>
              )}
            </div>

            {/* 流程跟踪 */}
            <Card style={{ marginBottom: '24px' }}>
              <CardHeader>
                <CardTitle style={{
                  fontSize: '18px',
                  fontWeight: '600',
                  color: '#1f2937',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <Clock style={{ width: '20px', height: '20px', color: '#3b82f6' }} />
                  流程状态
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  {/* 提交步骤 */}
                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                    <div style={{
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%',
                      backgroundColor: '#10b981',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <User style={{ width: '16px', height: '16px', color: 'white' }} />
                    </div>
                    <div style={{ flex: 1 }}>
                      <div style={{ fontSize: '14px', fontWeight: '500', color: '#1f2937' }}>提交</div>
                      <div style={{ fontSize: '12px', color: '#6b7280' }}>{orderInfo.proposer_email || "申请人"}</div>
                    </div>
                    <div style={{ fontSize: '12px', color: '#6b7280' }}>tanmingyu</div>
                  </div>

                  {/* 审批步骤 */}
                  {stageInfos.map((stage, index) => {
                    const isCurrentStage = index === orderInfo.current_stage - 1;
                    const isCompleted = stage.stage_result === 1;
                    const isRejected = stage.stage_result === 2;

                    let stepColor = '#e5e7eb';
                    let stepIcon = <Clock style={{ width: '16px', height: '16px', color: '#6b7280' }} />;

                    if (isCompleted) {
                      stepColor = '#10b981';
                      stepIcon = <CheckCircle style={{ width: '16px', height: '16px', color: 'white' }} />;
                    } else if (isRejected) {
                      stepColor = '#ef4444';
                      stepIcon = <X style={{ width: '16px', height: '16px', color: 'white' }} />;
                    } else if (isCurrentStage) {
                      stepColor = '#3b82f6';
                      stepIcon = <Clock style={{ width: '16px', height: '16px', color: 'white' }} />;
                    }

                    return (
                      <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                        <div style={{
                          width: '32px',
                          height: '32px',
                          borderRadius: '50%',
                          backgroundColor: stepColor,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          {stepIcon}
                        </div>
                        <div style={{ flex: 1 }}>
                          <div style={{ fontSize: '14px', fontWeight: '500', color: '#1f2937' }}>
                            {stage.stage_name || `指定审批人审批`}
                          </div>
                          <div style={{ fontSize: '12px', color: '#6b7280' }}>{stage.stage_operator}</div>
                        </div>
                        <div style={{ fontSize: '12px', color: '#6b7280' }}>
                          {isCurrentStage ? '运维审批' : 'tanmingyu'}
                        </div>
                      </div>
                    );
                  })}

                  {/* Done步骤 */}
                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                    <div style={{
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%',
                      backgroundColor: orderInfo.result === 1 ? '#10b981' : '#e5e7eb',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <CheckCircle style={{
                        width: '16px',
                        height: '16px',
                        color: orderInfo.result === 1 ? 'white' : '#6b7280'
                      }} />
                    </div>
                    <div style={{ flex: 1 }}>
                      <div style={{ fontSize: '14px', fontWeight: '500', color: '#1f2937' }}>Done</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 工单信息 */}
            <Card style={{ marginBottom: '24px' }}>
              <CardHeader>
                <CardTitle style={{
                  fontSize: '18px',
                  fontWeight: '600',
                  color: '#1f2937',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <FileText style={{ width: '20px', height: '20px', color: '#3b82f6' }} />
                  工单信息
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '24px 16px' }}>
                  <div>
                    <div style={{ fontSize: '12px', color: '#6b7280', marginBottom: '4px', fontWeight: '500' }}>工单ID</div>
                    <div style={{ fontSize: '14px', color: '#1f2937', fontWeight: '500' }}>{orderInfo.order_id}</div>
                  </div>
                  <div>
                    <div style={{ fontSize: '12px', color: '#6b7280', marginBottom: '4px', fontWeight: '500' }}>工单名称</div>
                    <div style={{ fontSize: '14px', color: '#1f2937', fontWeight: '500' }}>数据库性能优化工单</div>
                  </div>
                  <div>
                    <div style={{ fontSize: '12px', color: '#6b7280', marginBottom: '4px', fontWeight: '500' }}>工单负责人</div>
                    <div style={{ fontSize: '14px', color: '#1f2937', fontWeight: '500' }}>李四</div>
                  </div>
                  <div>
                    <div style={{ fontSize: '12px', color: '#6b7280', marginBottom: '4px', fontWeight: '500' }}>状态</div>
                    <Badge className={getOrderStatus(orderInfo.result).color} style={{ fontSize: '12px' }}>
                      <Clock style={{ width: '12px', height: '12px', marginRight: '4px' }} />
                      {getOrderStatus(orderInfo.result).text}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 工单详情 */}
            <Card style={{ marginBottom: '24px' }}>
              <CardHeader>
                <CardTitle style={{
                  fontSize: '18px',
                  fontWeight: '600',
                  color: '#1f2937',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <FileText style={{ width: '20px', height: '20px', color: '#3b82f6' }} />
                  工单详情
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div style={{ marginBottom: '16px' }}>
                  <div style={{ fontSize: '12px', color: '#6b7280', marginBottom: '8px', fontWeight: '500' }}>申请理由</div>
                  <div style={{
                    padding: '16px',
                    backgroundColor: '#f8fafc',
                    borderRadius: '8px',
                    fontSize: '14px',
                    lineHeight: '1.6',
                    color: '#374151',
                    border: '1px solid #e2e8f0'
                  }}>
                    标题: 数据库性能优化和维护<br />
                    内容: {orderInfo.apply_msg || '暂无申请理由'}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 评论区域 */}
            <Card style={{ marginBottom: '24px' }}>
              <CardHeader>
                <CardTitle style={{
                  fontSize: '18px',
                  fontWeight: '600',
                  color: '#1f2937',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <MessageSquare style={{ width: '20px', height: '20px', color: '#3b82f6' }} />
                  评论
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CustomTextarea
                  placeholder="添加评论..."
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                  style={{ marginBottom: '12px' }}
                />
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  fontSize: '12px',
                  color: '#6b7280'
                }}>
                  <span>Ctrl + Enter 快速提交</span>
                  <CustomButton
                    className="variant-default size-sm"
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '6px',
                      fontSize: '12px'
                    }}
                  >
                    <MessageSquare style={{ width: '14px', height: '14px' }} />
                    发表评论
                  </CustomButton>
                </div>
              </CardContent>
            </Card>
          </div>
        </Drawer>

      {/* 负责人变更弹窗 */}
      {showOwnerChangeModal && (
        <DialogOverlay onClick={() => setShowOwnerChangeModal(false)}>
          <DialogContent onClick={(e) => e.stopPropagation()}>
            <DialogHeader>
              <DialogTitle>
                <UserCheck className="h-5 w-5 text-blue-600" />
                负责人变更
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-6 px-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <User className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-blue-900 mb-1">当前负责人</h4>
                    <p className="text-sm text-blue-700">{orderInfo?.assignee || "未指定"}</p>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <CustomLabel className="text-sm font-medium text-gray-700">
                  新负责人邮箱 <span className="text-red-500">*</span>
                </CustomLabel>
                <CustomInput
                  type="email"
                  placeholder="请输入新负责人的邮箱地址"
                  value={newOwnerEmail}
                  onChange={(e) => setNewOwnerEmail(e.target.value)}
                />
                <p className="text-xs text-gray-500">系统将自动发送通知邮件给新负责人</p>
              </div>
            </div>
            <DialogFooter>
              <CustomButton
                className="variant-outline flex-1"
                onClick={() => setShowOwnerChangeModal(false)}
              >
                取消
              </CustomButton>
              <CustomButton
                className="flex-1"
                onClick={handleOwnerChange}
                disabled={!newOwnerEmail}
              >
                <UserCheck className="h-4 w-4 mr-2" />
                确认变更
              </CustomButton>
            </DialogFooter>
          </DialogContent>
        </DialogOverlay>
      )}

      {/* 审批弹窗 */}
      {showApprovalModal && (
        <DialogOverlay onClick={() => setShowApprovalModal(false)}>
          <DialogContent onClick={(e) => e.stopPropagation()}>
            <DialogHeader>
              <DialogTitle>
                {approvalAction === "approve" ? (
                  <ThumbsUp className="h-5 w-5 text-blue-600" />
                ) : (
                  <ThumbsDown className="h-5 w-5 text-red-600" />
                )}
                审批操作
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-6 px-6">
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <FileText className="h-4 w-4 text-gray-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">工单信息</h4>
                    <p className="text-sm text-gray-600">
                      {orderInfo?.title} - {orderType}
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <CustomLabel className="text-sm font-medium text-gray-700">审批决定</CustomLabel>
                <div className="grid grid-cols-2 gap-3">
                  <CustomButton
                    className={approvalAction === "approve" ? "variant-default" : "variant-outline"}
                    onClick={() => setApprovalAction("approve")}
                    style={{ height: '48px' }}
                  >
                    <ThumbsUp className="h-4 w-4 mr-2" />
                    同意
                  </CustomButton>
                  <CustomButton
                    className={approvalAction === "reject" ? "variant-destructive" : "variant-outline"}
                    onClick={() => setApprovalAction("reject")}
                    style={{ height: '48px' }}
                  >
                    <ThumbsDown className="h-4 w-4 mr-2" />
                    驳回
                  </CustomButton>
                </div>
              </div>

              <div className="space-y-3">
                <CustomLabel className="text-sm font-medium text-gray-700">备注</CustomLabel>
                <CustomTextarea
                  placeholder="请输入审批备注..."
                  value={remark}
                  onChange={(e) => setRemark(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <CustomButton
                className="variant-outline flex-1"
                onClick={() => setShowApprovalModal(false)}
              >
                取消
              </CustomButton>
              <CustomButton
                className={`flex-1 ${approvalAction === "reject" ? "variant-destructive" : ""}`}
                onClick={handleApproval}
              >
                {approvalAction === "approve" ? (
                  <ThumbsUp className="h-4 w-4 mr-2" />
                ) : (
                  <ThumbsDown className="h-4 w-4 mr-2" />
                )}
                确认{approvalAction === "approve" ? "同意" : "驳回"}
              </CustomButton>
            </DialogFooter>
          </DialogContent>
        </DialogOverlay>
      )}
    </>
  );
}
